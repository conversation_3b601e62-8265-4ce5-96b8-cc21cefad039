// 机械臂服务 RobotArmService.js
// 用于 uni-app 前端项目

import config from "../config";
import {ConfigService} from "@/services/ConfigService";
import {CountService} from "@/services/CountService";
import {CacheMgr} from "@/utils/CacheMgr";
import {showToast} from "@/utils/funs";
import {mobileLog, showModal, sleep} from "../utils/funs";

// 全局变量
const baseUrl = `https://${config.host}:${config.port}`;
let handle = null; // 资源号
let openHandleDebouncing = false; // 防抖处理
let currentPosition = {x: 0, y: 0}; // 当前机械臂位置


async function _fetch(url) {
    try {
        const res = await fetch(url);

        if (!res.ok) {
            showModal({
                title: `HTTP ${res.status}: 网络请求失败 ${res.statusText}`,
                showCancel: false,
            });
            return null;
        }

        return await res.json();

    } catch (err) {
        mobileLog('网络请求失败:', err.message);
        let errorMsg: string;

        if (err.name === 'TypeError' && err.message.includes('fetch')) {
            errorMsg = '无法连接到服务器，请检查网络连接';
        } else if (err.name === 'AbortError') {
            errorMsg = '请求超时，请重试';
        } else {
            errorMsg = `网络请求失败: ${err.message}`;
        }

        showModal({
            title: errorMsg,
            showCancel: false,
        });
        return null;
    }
}


/**
 * 机械臂服务类
 * 提供机械臂控制的实例方法
 */
export class RobotArmService {

    handle: number = 0;
    port: string = '';
    currentPosition: object = {x: 0, y: 0};

    constructor(handle: number, port: string) {
        this.handle = handle;
        this.port = port;
        this.currentPosition = {x: 0, y: 0};
    }

    /**
     * 打开端口，返回RobotArmService实例
     * @param {string} port 端口名，如 'COM4'
     */
    static async openHandle(port: string = 'COM4') {
        // 防抖处理，防止连续点击
        if (openHandleDebouncing) {
            mobileLog('正在处理中，请稍候...');
            return;
        }

        openHandleDebouncing = true;
        try {
            const data = await _fetch(`${baseUrl}/robot-arm/open?port=${encodeURIComponent(port)}`);

            if (data && data.success && data.handle) {
                handle = data.handle;

                // 初始化当前位置为零点
                currentPosition.x = 0;
                currentPosition.y = 0;

                // 保存端口缓存
                CacheMgr.set(`port2handle${port}`, handle);

                // 显示成功提示
                showToast({
                    title: '端口打开成功',
                    icon: 'success',
                    duration: 1500
                });
                mobileLog('端口打开成功');

                await sleep(200);

                // 返回RobotArmService实例
                return new RobotArmService(handle, port);
            } else {
                const errorMsg = '端口打开失败，返回值：' + JSON.stringify(data);
                showModal({
                    title: errorMsg,
                    showCancel: false,
                });
            }
        } catch (err) {
            // 显示错误提示
            const errorMsg = `打开端口失败: ${err.message}`;
            showModal({
                title: errorMsg,
                showCancel: false,
            });
        } finally {
            openHandleDebouncing = false;
        }

    }

    /**
     * 传入屏幕像素移动到机械臂指定位置
     * 这里传入的是 屏幕像素，然后会换算到机械臂物理世界坐标
     * @param x
     * @param y
     * @returns {Promise<void>}
     */
    async moveToByPx(x: number, y: number): Promise<void> {
        let moveX = x;
        let moveY = y;
        moveX = moveX / CountService.getPxWidth()
        moveY = moveY / CountService.getPxHeight()

        moveX += ConfigService.getOffsetX();
        moveY += ConfigService.getOffsetY();

        moveX = Math.ceil(moveX);
        moveY = Math.ceil(moveY);

        await this.moveTo(moveX, moveY);
    }


    /**
     * 移动到指定坐标
     * 这传入的是 机械臂的XY
     * @param {number} x
     * @param {number} y
     */
    async moveTo(x: number, y: number) {
        // 对x和y进行四舍五入，确保是整数
        x = Math.round(x);
        y = Math.round(y);

        if (!this.handle) {
            showModal({
                title: "机械臂端口未打开",
                showCancel: false,
            });
            return false;
        }

        try {
            // 读取XY轴最大移动距离配置
            const maxMoveX = ConfigService.getMaxMoveX();
            const maxMoveY = ConfigService.getMaxMoveY();

            // 限制移动距离不超过最大值
            const limitedX = Math.min(Math.max(x, 0), maxMoveX);
            const limitedY = Math.min(Math.max(y, 0), maxMoveY);

            // 如果坐标被限制了，输出日志提示
            if (limitedX !== x || limitedY !== y) {
                mobileLog(`移动坐标被限制: 原坐标(${x}, ${y}) -> 限制后坐标(${limitedX}, ${limitedY}), 最大移动距离: X=${maxMoveX}, Y=${maxMoveY}`);
                await uni.showToast({
                    title: '坐标已自动调整到安全范围',
                    icon: 'none',
                    duration: 2000
                });
            }

            // 计算移动距离
            const deltaX = Math.abs(limitedX - this.currentPosition.x);
            const deltaY = Math.abs(limitedY - this.currentPosition.y);
            const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            mobileLog(`从位置 (${this.currentPosition.x}, ${this.currentPosition.y}) 移动到 (${limitedX}, ${limitedY}), 移动距离: ${moveDistance.toFixed(2)}`);

            const data = await _fetch(`${baseUrl}/robot-arm/move?handle=${encodeURIComponent(this.handle)}&x=${limitedX}&y=${limitedY}`);

            if (data === null) {
                return false;
            }

            // 更新当前位置
            this.currentPosition.x = limitedX;
            this.currentPosition.y = limitedY;

            // 根据移动距离计算需要等待的时间
            // 机械臂速度约0.12单位/ms，基础等待50ms确保稳定
            const waitTime = Math.max(50, Math.round(50 + moveDistance / 0.19));
            mobileLog(`移动到坐标 (${limitedX}, ${limitedY}) 成功，等待 ${waitTime}ms 确保移动到位`);

            // 等待机械臂移动到位
            await sleep(waitTime);

            return true;

        } catch (err) {
            mobileLog('移动失败:', err.message);
            const errorMsg = `移动失败: ${err.message}`;
            error(errorMsg);
            return false;
        }
    }

    /**
     * 按下（触控笔下降）
     * @param {number} z 距离，默认6mm
     */
    async press(z = 8) {
        if (!this.handle) {
            error("机械臂端口未打开");
            return false;
        }

        try {
            const data = await _fetch(`${baseUrl}/robot-arm/press?handle=${encodeURIComponent(this.handle)}&z=${z}`);

            if (data === null) {
                return false;
            }


            await sleep(50);

            return true;

        } catch (err) {
            mobileLog('按下失败:', err.message);
            const errorMsg = `按下失败: ${err.message}`;
            error(errorMsg);
            return false;
        }
    }

    /**
     * 抬起（触控笔抬起）
     */
    async release() {
        if (!this.handle) {
            error("机械臂端口未打开");
            return false;
        }

        try {
            const data = await _fetch(`${baseUrl}/robot-arm/release?handle=${encodeURIComponent(this.handle)}`);

            if (data === null) {
                return false;
            }

            mobileLog('抬起成功');

            // 等待机械臂移动到位
            await sleep(100);

            return true;

        } catch (err) {
            mobileLog('抬起失败:', err.message);
            const errorMsg = `抬起失败: ${err.message}`;
            error(errorMsg);
            return false;
        }
    }

    /**
     * 点击（移动到坐标后按下再抬起）
     * @param {number} x
     * @param {number} y
     */
    async click(x, y) {
        try {
            mobileLog(`开始点击坐标 (${x}, ${y})`);

            // 移动到指定位置
            const moveSuccess = await this.moveTo(x, y);
            if (!moveSuccess) {
                return false;
            }

            // 按下
            const pressSuccess = await this.press();
            if (!pressSuccess) {
                return false;
            }

            // 抬起
            const releaseSuccess = await this.release();
            if (!releaseSuccess) {
                return false;
            }

            mobileLog(`点击坐标 (${x}, ${y}) 完成`);
            return true;

        } catch (err) {
            mobileLog('点击操作失败:', err.message);
            error(`点击失败: ${err.message}`);
            return false;
        }
    }

    /**
     * 滑动（从起点滑到终点，默认分8步）
     * @param {number} x1
     * @param {number} y1
     * @param {number} x2
     * @param {number} y2
     */
    async swipe(x1, y1, x2, y2) {
        try {
            mobileLog(`开始滑动: (${x1}, ${y1}) -> (${x2}, ${y2})`);

            const moveToStartSuccess = await this.moveTo(x1, y1);
            if (!moveToStartSuccess) {
                return false;
            }

            const pressSuccess = await this.press();
            if (!pressSuccess) {
                return false;
            }

            const moveToEndSuccess = await this.moveTo(x2, y2);
            if (!moveToEndSuccess) {
                return false;
            }

            const releaseSuccess = await this.release();
            if (!releaseSuccess) {
                return false;
            }

            mobileLog(`滑动完成: (${x1}, ${y1}) -> (${x2}, ${y2})`);
            return true;

        } catch (err) {
            mobileLog('滑动操作失败:', err.message);
            error(`滑动失败: ${err.message}`);

            // 尝试抬起触控笔，避免卡住
            try {
                await this.release();
            } catch (releaseErr) {
                mobileLog('滑动失败后抬起也失败:', releaseErr.message);
            }

            return false;
        }
    }

    /**
     * 回到零点（复位）
     */
    async reset() {
        if (!this.handle) {
            error("机械臂端口未打开");
            return false;
        }

        try {
            return await this.moveTo(0, 0);
        } catch (err) {
            mobileLog('复位失败:', err.message);
            const errorMsg = `复位失败: ${err.message}`;
            error(errorMsg);
            return false;
        }
    }

    /**
     * 关闭端口
     */
    async closeHandle() {
        if (!this.handle) {
            mobileLog('没有打开的端口需要关闭');
            return true;
        }

        try {
            mobileLog(`开始关闭端口，句柄: ${this.handle}`);
            const url = `${baseUrl}/robot-arm/close?handle=${encodeURIComponent(this.handle)}`;
            const data = await _fetch(url, {timeout: 5000});

            if (data === null) {
                // 即使关闭失败，也清除本地句柄和位置状态，避免状态不一致
                this.handle = null;
                this.currentPosition.x = 0;
                this.currentPosition.y = 0;
                // 清空端口缓存
                clearPortCache();
                // 网络错误时可能是正常断开，不返回false
                mobileLog('网络连接失败，可能端口已正常断开');
                return true;
            }

            mobileLog('关闭端口结果:', data);

            // 清除本地句柄和位置状态
            this.handle = null;
            this.currentPosition.x = 0;
            this.currentPosition.y = 0;

            // 清空端口缓存
            clearPortCache();

            // 显示成功提示
            await uni.showToast({
                title: '端口已关闭',
                icon: 'success',
                duration: 1500
            });

            return true;

        } catch (err) {
            mobileLog('关闭端口失败:', err.message);

            // 即使关闭失败，也清除本地句柄和位置状态，避免状态不一致
            this.handle = null;
            this.currentPosition.x = 0;
            this.currentPosition.y = 0;

            // 清空端口缓存
            clearPortCache();

            const errorMsg = `关闭端口失败: ${err.message}`;
            error(errorMsg);
            return false;
        }
    }
}